import React from 'react';
import { useTranslation } from 'react-i18next';
import { Alert, StyleSheet, Switch, View } from 'react-native';

import { useLanguage } from '@/app/LanguageContext';
import { ThemeName, useAppTheme } from '@/app/ThemeContext';
import ParallaxScrollView from '@/components/ParallaxScrollView';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { FuturisticButton } from '@/components/ui/FuturisticButton';
import { FuturisticCard } from '@/components/ui/FuturisticCard';
import { FuturisticGrid } from '@/components/ui/FuturisticGrid';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { LANGUAGES } from '@/constants/Languages';
import { getResponsiveFontSize, getResponsiveSpacing, isSmallScreen, responsiveDimensions } from '../../utils/responsive';

export default function SettingsScreen() {
  const { t } = useTranslation();
  const { themeName, setTheme, colorScheme, glowEnabled, setGlowEnabled } = useAppTheme();
  const colors = Colors[colorScheme ?? 'light'];
  const { language, setLanguage } = useLanguage();
  const { signOut, isLoading } = useAuthStore();

  // Theme options
  const themeOptions: ThemeName[] = ['system', 'superBlack', 'boxyBeige'];

  const handleLogout = () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: async () => {
            try {
              await signOut();
            } catch (error) {
              Alert.alert('Error', 'Failed to sign out. Please try again.');
            }
          }
        }
      ]
    );
  };

  return (
    <ThemedView style={styles.container}>
      <FuturisticGrid size={40} opacity={0.1} />
      
      <ParallaxScrollView
        headerBackgroundColor={{
          light: Colors.light.background,
          dark: Colors.dark.background
        }}
        headerImage={
          <ThemedView style={styles.headerImageContainer}>
            <IconSymbol
              size={responsiveDimensions.header.iconSize}
              name="gearshape.fill"
              color={colors.tint}
            />
            <ThemedText type="title" style={[styles.headerTitle, { color: colors.tint }]}>
              {t('settings.title')}
            </ThemedText>
            <ThemedText style={[styles.headerSubtitle, { color: colors.textSecondary }]}>
              {t('subtitles.settings')}
            </ThemedText>
          </ThemedView>
        }>
        <ThemedView style={styles.content}>
          {/* Theme Section */}
          <ThemedView style={styles.section}>
            <ThemedText style={[styles.sectionTitle, { color: colors.tint }]}>
              {t('settings.theme')}
            </ThemedText>
            <ThemedText style={[styles.sectionDesc, { color: colors.textSecondary }]}>
              {t('settings.themeDescription')}
            </ThemedText>

            <ThemedView style={styles.optionsGrid}>
              {themeOptions.map((theme) => (
                <FuturisticCard
                  key={theme}
                  variant={themeName === theme ? "neon" : "glass"}
                  style={styles.optionCard}
                  glowIntensity={themeName === theme ? 2 : 0.5}
                  onPress={() => setTheme(theme)}
                >
                  <ThemedText
                    style={[
                      styles.optionTitle,
                      { color: themeName === theme ? colors.tint : colors.text }
                    ]}>
                    {t(`themes.${theme}`)}
                  </ThemedText>
                  <ThemedText style={[styles.optionDesc, { color: colors.textSecondary }]}>
                    {t(`themes.${theme}Desc`)}
                  </ThemedText>

                  {/* Theme preview */}
                  <ThemedView
                    style={[
                      styles.themePreview,
                      {
                        backgroundColor:
                          theme === 'superBlack' ? '#000000' :
                          theme === 'boxyBeige' ? '#F2E8CF' :
                          colorScheme === 'dark' ? '#000000' : '#F2E8CF'
                      }
                    ]}>
                    <ThemedView
                      style={[
                        styles.themePreviewAccent,
                        {
                          backgroundColor:
                            theme === 'superBlack' ? '#C0C0C0' :
                            theme === 'boxyBeige' ? '#C8963E' :
                            colorScheme === 'dark' ? '#C0C0C0' : '#C8963E'
                        }
                      ]}
                    />
                  </ThemedView>
                </FuturisticCard>
              ))}
            </ThemedView>
          </ThemedView>

          {/* Enhanced Glow Section */}
          {colorScheme === 'dark' && (
            <ThemedView style={styles.section}>
              <ThemedText style={[styles.sectionTitle, { color: colors.tint }]}>
                {t('settings.glowEffects')}
              </ThemedText>
              <ThemedText style={[styles.sectionDesc, { color: colors.textSecondary }]}>
                {t('settings.glowEffectsDescription')}
              </ThemedText>

              <FuturisticCard
                variant={glowEnabled ? "neon" : "glass"}
                style={styles.optionCard}
                glowIntensity={glowEnabled ? 2 : 0.5}
              >
                <ThemedView style={styles.settingRow}>
                  <ThemedView>
                    <ThemedText
                      style={[
                        styles.optionTitle,
                        { color: glowEnabled ? colors.tint : colors.text }
                      ]}>
                      {t('settings.enhancedGlow')}
                    </ThemedText>
                    <ThemedText style={[styles.optionDesc, { color: colors.textSecondary }]}>
                      {t('settings.enhancedGlowDescription')}
                    </ThemedText>
                  </ThemedView>
                  <Switch
                    value={glowEnabled}
                    onValueChange={setGlowEnabled}
                    trackColor={{ false: colors.border, true: colors.tint }}
                    thumbColor={glowEnabled ? '#ffffff' : colors.icon}
                  />
                </ThemedView>
              </FuturisticCard>
            </ThemedView>
          )}

          {/* Language Section */}
          <ThemedView style={styles.section}>
            <ThemedText style={[styles.sectionTitle, { color: colors.tint }]}>
              {t('settings.language')}
            </ThemedText>
            <ThemedText style={[styles.sectionDesc, { color: colors.textSecondary }]}>
              {t('settings.languageDescription')}
            </ThemedText>

            <ThemedView style={styles.optionsGrid}>
              {LANGUAGES.map((item) => (
                <FuturisticCard
                  key={item.code}
                  variant={language === item.code ? "neon" : "glass"}
                  style={styles.optionCard}
                  glowIntensity={language === item.code ? 2 : 0.5}
                  onPress={() => setLanguage(item.code)}
                >
                  <View style={styles.languageRow}>
                    <ThemedText style={styles.languageFlag}>{item.flag}</ThemedText>
                    <ThemedText
                      style={[
                        styles.optionTitle,
                        { color: language === item.code ? colors.tint : colors.text }
                      ]}>
                      {item.nativeName}
                    </ThemedText>
                  </View>
                  <ThemedText style={[styles.optionDesc, { color: colors.textSecondary }]}>
                    {item.name}
                  </ThemedText>
                </FuturisticCard>
              ))}
            </ThemedView>
          </ThemedView>

          {/* Logout Section */}
          <ThemedView style={styles.section}>
            <FuturisticButton
              title="Sign Out"
              variant="outline"
              size="large"
              onPress={handleLogout}
              disabled={isLoading}
              style={styles.logoutButton}
            />
          </ThemedView>
        </ThemedView>
      </ParallaxScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
  headerImageContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: getResponsiveSpacing('lg'),
    gap: getResponsiveSpacing('sm'),
  },
  headerTitle: {
    fontSize: getResponsiveFontSize('header'),
    fontWeight: '700',
    textAlign: 'center',
    letterSpacing: 1,
  },
  headerSubtitle: {
    fontSize: getResponsiveFontSize('sm'),
    textAlign: 'center',
    opacity: 0.8,
    letterSpacing: 0.5,
  },
  content: {
    gap: getResponsiveSpacing('xl'),
    paddingBottom: getResponsiveSpacing('xl'),
  },
  section: {
    gap: getResponsiveSpacing('md'),
  },
  sectionTitle: {
    fontSize: getResponsiveFontSize('lg'),
    fontWeight: '600',
    letterSpacing: 0.5,
  },
  sectionDesc: {
    fontSize: getResponsiveFontSize('sm'),
    opacity: 0.8,
    marginBottom: getResponsiveSpacing('xs'),
  },
  optionsGrid: {
    gap: getResponsiveSpacing('md'),
    marginTop: getResponsiveSpacing('sm'),
  },
  optionCard: {
    padding: getResponsiveSpacing('md'),
    minHeight: responsiveDimensions.cardHeight.md,
  },
  optionTitle: {
    fontSize: getResponsiveFontSize('md'),
    fontWeight: '600',
    marginBottom: getResponsiveSpacing('xs'),
  },
  optionDesc: {
    fontSize: getResponsiveFontSize('sm'),
    opacity: 0.8,
    marginBottom: getResponsiveSpacing('sm'),
  },
  languageRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  languageFlag: {
    fontSize: getResponsiveFontSize('xl'),
    marginRight: getResponsiveSpacing('sm'),
  },
  themePreview: {
    height: isSmallScreen ? 50 : 60,
    borderRadius: 8,
    position: 'relative',
    overflow: 'hidden',
  },
  themePreviewAccent: {
    position: 'absolute',
    height: isSmallScreen ? 16 : 20,
    width: isSmallScreen ? 60 : 80,
    bottom: getResponsiveSpacing('xs'),
    right: getResponsiveSpacing('xs'),
    borderRadius: 4,
  },
  logoutButton: {
    marginTop: getResponsiveSpacing('lg'),
    alignSelf: 'center',
    minWidth: 200,
  }
});