import React, { createContext, useContext, useEffect, ReactNode } from 'react';
import { User } from 'firebase/auth';
import { authService } from '../services/authService';
import { useAuthStore } from '../stores/authStore';
import { useAppLockStore } from '../stores/appLockStore';

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  isLoading: true,
  isAuthenticated: false,
});

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const { user, isLoading, isAuthenticated, setUser, setUserProfile, setLoading } = useAuthStore();
  const { loadUserData } = useAppLockStore();

  useEffect(() => {
    const unsubscribe = authService.onAuthStateChanged(async (user) => {
      setUser(user);
      
      if (user) {
        try {
          // Load user profile
          const profile = await authService.getUserProfile(user.uid);
          setUserProfile(profile);
          
          // Load user app lock data
          await loadUserData(user.uid);
        } catch (error) {
          console.error('Error loading user data:', error);
        }
      } else {
        setUserProfile(null);
      }
      
      setLoading(false);
    });

    return unsubscribe;
  }, [setUser, setUserProfile, setLoading, loadUserData]);

  const contextValue: AuthContextType = {
    user,
    isLoading,
    isAuthenticated,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
