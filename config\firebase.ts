import AsyncStorage from '@react-native-async-storage/async-storage';
import { getAnalytics, isSupported } from 'firebase/analytics';
import { getApps, initializeApp } from 'firebase/app';
import { getAuth, getReactNativePersistence, initializeAuth } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';
import { getFunctions } from 'firebase/functions';
import { Platform } from 'react-native';

// Firebase configuration using your project details
const firebaseConfig = {
  apiKey: "AIzaSyAsAOt7GGkWKKJLZp3MVdozcUB_ghoLqOc",
  authDomain: "feefence.firebaseapp.com",
  projectId: "feefence",
  storageBucket: "feefence.firebasestorage.app",
  messagingSenderId: "83272932146",
  appId: Platform.select({
    web: "1:83272932146:web:8919ce6c404d8d1497d52b",
    android: "1:83272932146:android:9cd33a930093226a97d52b",
    ios: "1:83272932146:android:9cd33a930093226a97d52b", // You'll need to create iOS app in Firebase
    default: "1:83272932146:web:8919ce6c404d8d1497d52b"
  }),
  measurementId: "G-T0CJFQW6JB"
};

// Initialize Firebase only if it hasn't been initialized yet
let app;
if (getApps().length === 0) {
  app = initializeApp(firebaseConfig);
} else {
  app = getApps()[0];
}

// Initialize Firebase Auth with AsyncStorage persistence for React Native
let auth;
try {
  if (Platform.OS === 'web') {
    // For web, use the default auth
    auth = getAuth(app);
  } else {
    // For React Native, use AsyncStorage persistence
    auth = initializeAuth(app, {
      persistence: getReactNativePersistence(AsyncStorage)
    });
  }
} catch (error) {
  // If auth is already initialized, get the existing instance
  auth = getAuth(app);
}

// Initialize Firestore
export const db = getFirestore(app);

// Initialize Functions
export const functions = getFunctions(app);

// Initialize Analytics (only for web and when supported)
let analytics = null;
if (Platform.OS === 'web') {
  isSupported().then((supported) => {
    if (supported) {
      analytics = getAnalytics(app);
    }
  });
}

// Export auth
export { analytics, auth };

// Export the app instance
export default app;
