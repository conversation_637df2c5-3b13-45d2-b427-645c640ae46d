import { Platform } from 'react-native';

// Platform-specific Firebase imports and initialization
let auth: any, db: any, functions: any;

if (Platform.OS === 'web') {
  // Web Firebase SDK
  const { initializeApp, getApps } = require('firebase/app');
  const { getAuth } = require('firebase/auth');
  const { getFirestore } = require('firebase/firestore');
  const { getFunctions } = require('firebase/functions');

  const firebaseConfig = {
    apiKey: "AIzaSyAsAOt7GGkWKKJLZp3MVdozcUB_ghoLqOc",
    authDomain: "feefence.firebaseapp.com",
    projectId: "feefence",
    storageBucket: "feefence.firebasestorage.app",
    messagingSenderId: "83272932146",
    appId: "1:83272932146:web:8919ce6c404d8d1497d52b",
    measurementId: "G-T0CJFQW6JB"
  };

  let app;
  if (getApps().length === 0) {
    app = initializeApp(firebaseConfig);
  } else {
    app = getApps()[0];
  }

  auth = getAuth(app);
  db = getFirestore(app);
  functions = getFunctions(app);
} else {
  // React Native Firebase for mobile platforms
  const rnAuth = require('@react-native-firebase/auth').default;
  const rnFirestore = require('@react-native-firebase/firestore').default;
  const rnFunctions = require('@react-native-firebase/functions').default;

  // Initialize React Native Firebase (uses google-services.json automatically)
  auth = rnAuth();
  db = rnFirestore();
  functions = rnFunctions();
}

export { auth, db, functions };

