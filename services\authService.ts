import { auth, db } from '../config/firebase';

// Define User type that works for both platforms
export interface User {
  uid: string;
  email: string | null;
  displayName?: string | null;
}

export interface UserProfile {
  uid: string;
  email: string;
  displayName?: string;
  createdAt: Date;
  updatedAt: Date;
  preferences: {
    theme: 'system' | 'superBlack' | 'boxyBeige';
    language: string;
    glowEnabled: boolean;
    currency: string;
  };
  settings: {
    masterLockEnabled: boolean;
    monthlyFeeLimit: number;
    gracePeriodsUsed: number;
    gracePeriodsResetDate: Date;
  };
}

class AuthService {
  // Sign up with email and password
  async signUp(email: string, password: string, displayName?: string): Promise<User> {
    try {
      const userCredential = await auth.createUserWithEmailAndPassword(email, password);
      const user = userCredential.user;

      // Update display name if provided (React Native Firebase method)
      if (displayName && user.updateProfile) {
        await user.updateProfile({ displayName });
      }

      // Create user profile in Firestore
      await this.createUserProfile(user, displayName);

      return user;
    } catch (error) {
      console.error('Sign up error:', error);
      throw error;
    }
  }

  // Sign in with email and password
  async signIn(email: string, password: string): Promise<User> {
    try {
      const userCredential = await auth.signInWithEmailAndPassword(email, password);
      return userCredential.user;
    } catch (error) {
      console.error('Sign in error:', error);
      throw error;
    }
  }

  // Sign out
  async signOut(): Promise<void> {
    try {
      await auth.signOut();
    } catch (error) {
      console.error('Sign out error:', error);
      throw error;
    }
  }

  // Create user profile in Firestore
  private async createUserProfile(user: User, displayName?: string): Promise<void> {
    const userProfile: UserProfile = {
      uid: user.uid,
      email: user.email!,
      displayName: displayName || user.displayName || undefined,
      createdAt: new Date(),
      updatedAt: new Date(),
      preferences: {
        theme: 'system',
        language: 'en',
        glowEnabled: true,
        currency: 'USD',
      },
      settings: {
        masterLockEnabled: false,
        monthlyFeeLimit: 20, // Default $20/month limit
        gracePeriodsUsed: 0,
        gracePeriodsResetDate: new Date(),
      },
    };

    // Use React Native Firebase syntax for both platforms
    await db.collection('users').doc(user.uid).set(userProfile);
  }

  // Get user profile from Firestore
  async getUserProfile(uid: string): Promise<UserProfile | null> {
    try {
      const docSnap = await db.collection('users').doc(uid).get();

      if (docSnap.exists) {
        return docSnap.data() as UserProfile;
      } else {
        return null;
      }
    } catch (error) {
      console.error('Get user profile error:', error);
      throw error;
    }
  }

  // Update user profile
  async updateUserProfile(uid: string, updates: Partial<UserProfile>): Promise<void> {
    try {
      await db.collection('users').doc(uid).update({
        ...updates,
        updatedAt: new Date()
      });
    } catch (error) {
      console.error('Update user profile error:', error);
      throw error;
    }
  }

  // Listen to auth state changes
  onAuthStateChanged(callback: (user: User | null) => void) {
    return auth.onAuthStateChanged(callback);
  }

  // Get current user
  getCurrentUser(): User | null {
    return auth.currentUser;
  }
}

export const authService = new AuthService();
